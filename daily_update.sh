#!/usr/bin/env zsh
# ==============================================================================
#
#          FILE: arch-update.sh
#
#         USAGE: ./arch-update.sh [options]
#
#   DESCRIPTION: 一个用于 Arch Linux 的自动化每日更新与维护脚本。
#                它负责系统更新、缓存清理、孤儿包移除以及其他组件的维护。
#
#       OPTIONS: ---
#  REQUIREMENTS: zsh, paru, sudo, and other optional tools like flatpak, xmake.
#          BUGS: ---
#         NOTES: ---
#        AUTHOR: ---
#  ORGANIZATION: ---
#       CREATED: ---
#      REVISION: 2.1
#
# ==============================================================================

# 脚本严格模式，确保代码的健壮性和可预测性
# -e: 命令失败时立即退出
# -u: 使用未定义的变量时报错
# -o pipefail: 管道中的任何命令失败，整个管道都视为失败
set -euo pipefail

# ========================
# 常量与全局配置
# ========================
# 使用 typeset -gr (global readonly) 定义全局只读常量，防止在脚本中被意外修改。
typeset -gr PACMAN_LOCK="/var/lib/pacman/db.lck"    # Pacman 锁文件路径，用于检测更新进程是否在运行。
typeset -gr ICON_SCRIPT="$HOME/APP/Scripts/fix_app_icons/fix_app_icons.sh" # 图标修复脚本路径。
typeset -gr PACCACHE_KEEP=2                         # 使用 paccache 清理时，为每个包保留的最近版本数。
typeset -gr ORPHAN_CLEANUP_DAY="周一"                # 每周一允许执行孤儿包清理。
typeset -gr UV_CLEANUP_DAY="周一"                    # 每周一允许执行 UV 缓存清理。
typeset -gr GNOME_CACHE_CLEANUP_DAY="周一"           # 每周一允许执行 GNOME 缓存清理。
typeset -gr MIRRORS_UPDATE_DAY="周一"                # 每周一允许执行镜像源更新。
typeset -gr MIRRORS_STATE_FILE="$HOME/.cache/arch_update_mirrors_state" # 镜像源更新状态文件。
typeset -gr PARU_CACHE_DIR="$HOME/.cache/paru/clone" # Paru (AUR 助手) 的构建缓存目录。
typeset -gr ZINIT_SCRIPT_PATH="/usr/share/zinit/zinit.zsh"

# ========================
# 命令行参数解析
# ========================
# 默认值
VERBOSE_MODE=true           # 详细模式，输出所有日志信息。
PARALLEL_MODE=true          # 并行模式，同时更新 flatpak, xmake 等组件以节省时间。
ENABLE_SYSTEM_CLEANUP=true  # 启用额外的系统清理（如缩略图缓存等）。

# 解析传入脚本的命令行参数。
while [[ $# -gt 0 ]]; do
  case $1 in
    -q|--quiet) VERBOSE_MODE=false; shift ;;
    --no-parallel) PARALLEL_MODE=false; shift ;;
    --no-cleanup) ENABLE_SYSTEM_CLEANUP=false; shift ;;
    -h|--help)
      # 显示帮助信息并退出。
      echo "用法: $0 [选项]"
      echo "选项:"
      echo "  -q, --quiet      简洁模式输出"
      echo "  --no-parallel    禁用并行处理"
      echo "  --no-cleanup     禁用系统清理"
      echo "  -h, --help       显示此帮助信息"
      exit 0 ;;
    *) echo "未知选项: $1"; exit 1 ;;
  esac
done

# ========================
# 全局变量
# ========================
typeset -gA SCRIPT_STATS      # 全局关联数组，用于存储脚本运行的统计数据，如耗时、错误数等。
typeset -gA COMMAND_AVAILABLE # 全局关联数组，用于缓存命令是否存在的结果，避免重复检查。
typeset -g temp_dir           # 全局临时目录，用于在并行子进程之间传递状态信息。

# ========================
# 颜色定义与日志函数
# ========================
# 检测标准输出是否连接到终端，如果是，则启用颜色输出。
if [[ -t 1 ]]; then
  typeset -gr GREEN='\033[0;32m' RED='\033[0;31m' YELLOW='\033[0;33m'
  typeset -gr BLUE='\033[0;34m' CYAN='\033[0;36m' BOLD='\033[1m' NC='\033[0m'
  # 优化的边框颜色 - 使用更柔和的蓝紫色
  typeset -gr BORDER='\033[38;5;75m'     # 亮蓝色 (更现代化)
  typeset -gr BORDER_BOLD='\033[1;38;5;75m'  # 加粗亮蓝色
  typeset -gr CHECKMARK='✓' CROSSMARK='✗' ARROW='→' GEAR='⚙'
else
  # 如果不是终端（例如在 cron 任务中或重定向到文件），则禁用颜色和特殊字符。
  typeset -gr GREEN='' RED='' YELLOW='' BLUE='' CYAN='' BOLD='' NC=''
  typeset -gr BORDER='' BORDER_BOLD=''
  typeset -gr CHECKMARK='[OK]' CROSSMARK='[FAIL]' ARROW='->' GEAR='[WORK]'
fi

CURRENT_STEP=0
log_time() { date +'%H:%M'; } # 获取当前时间戳。
show_progress() { # 显示当前进度的标题。
  CURRENT_STEP=$((CURRENT_STEP + 1))
  printf "${BOLD}${CYAN}[%2d/%d]${NC} ${GEAR} %s\n" "$CURRENT_STEP" "$TOTAL_STEPS" "$1"
}
log() { [[ "$VERBOSE_MODE" == "true" ]] && echo -e "${BLUE}[$(log_time)] $*${NC}"; } # 普通日志，仅在详细模式下显示。
log_success() { echo -e "${GREEN}[$(log_time)] ${CHECKMARK} $*${NC}"; } # 成功日志。
log_warning() { # 警告日志，并增加警告计数。
  (( SCRIPT_STATS[warnings]++ ))
  echo -e "${YELLOW}[$(log_time)] ⚠ $*${NC}"
}
log_error() { # 错误日志，并增加错误计数，输出到 stderr。
  (( SCRIPT_STATS[errors]++ ))
  echo -e "${RED}[$(log_time)] ${CROSSMARK} $*${NC}" >&2
}

# ========================
# 核心功能函数
# ========================

# 将系统日期格式转换为"周X"格式
get_chinese_weekday() {
  local day=$(LC_ALL=zh_CN.UTF-8 date +'%a')
  case "$day" in
    "一") echo "周一" ;;
    "二") echo "周二" ;;
    "三") echo "周三" ;;
    "四") echo "周四" ;;
    "五") echo "周五" ;;
    "六") echo "周六" ;;
    "日") echo "周日" ;;
    *) echo "$day" ;;  # 兜底返回原值
  esac
}

# 检查镜像源是否已在本周更新过
check_mirrors_updated_this_week() {
  local current_week=$(date +%Y-%U)  # 获取当前年份和周数
  if [[ -f "$MIRRORS_STATE_FILE" ]]; then
    local last_update_week=$(<"$MIRRORS_STATE_FILE")
    [[ "$current_week" == "$last_update_week" ]]
  else
    return 1  # 文件不存在，表示未更新过
  fi
}

# 记录镜像源更新状态
record_mirrors_update() {
  local current_week=$(date +%Y-%U)
  mkdir -p "$(dirname "$MIRRORS_STATE_FILE")"
  echo "$current_week" > "$MIRRORS_STATE_FILE"
}

# 以 root 权限安全地运行命令。
run_sudo() {
  sudo -nv 2>/dev/null || sudo -v
  sudo "$@"
}

# 检查所有必需和可选的命令是否存在。
check_commands() {
  # `paru` 和 `bc` 是关键依赖。
  local commands=("fastfetch" "flatpak" "xmake" "go" "curl" "paccache" "bc" "paru" "uv" "reflector" "journalctl")
  for cmd in "${commands[@]}"; do
    if command -v "$cmd" &>/dev/null; then
      COMMAND_AVAILABLE[$cmd]=true
    else
      COMMAND_AVAILABLE[$cmd]=false
      if [[ "$cmd" == "bc" || "$cmd" == "paru" ]]; then
        log_error "关键命令 '$cmd' 未找到。请安装它。"
        return 1
      fi
    fi
  done

  # zinit 通常是一个 shell 函数而非独立命令，因此 `command -v` 会失败。
  # 正确的检测方法是检查其核心脚本文件是否存在，这个文件也是 update_zinit 函数实际依赖的。
  if [[ -f "$ZINIT_SCRIPT_PATH" ]]; then
    COMMAND_AVAILABLE[zinit]=true
  else
    COMMAND_AVAILABLE[zinit]=false
  fi

  return 0
}

# 检查网络连接。
check_network() {
  local endpoints=("http://connectivitycheck.gstatic.com/generate_204" "http://www.msftconnecttest.com/connecttest.txt" "http://detectportal.firefox.com/success.txt")
  for endpoint in "${endpoints[@]}"; do
    if curl -Is --connect-timeout 3 --max-time 5 "$endpoint" &>/dev/null; then return 0; fi
  done
  return 1
}

# 带重试机制的命令执行函数。
run_with_retry() {
  (( $# < 3 )) && { log_error "run_with_retry 使用错误"; return 1; }
  local max_retries=$1; local retry_delay=$2; shift 2; local cmd=("$@")
  for attempt in {1..$max_retries}; do
    if "${cmd[@]}"; then return 0; fi
    log_warning "命令 '${cmd[*]}' 尝试 $attempt/$max_retries 失败..."
    (( attempt < max_retries )) && { log_warning "将在 ${retry_delay} 秒后重试"; sleep "$retry_delay"; }
  done
  log_error "命令 '${cmd[*]}' 最终执行失败"
  return 1
}

# 检查根分区磁盘空间。
check_disk_space() {
  local min_free_gb=2
  local available_kb
  available_kb=$(df --output=avail / | tail -n 1 | tr -d ' ' 2>/dev/null || echo 0)
  local available_gb=$((available_kb / 1024 / 1024))
  if (( available_gb < min_free_gb )); then
    log_warning "磁盘空间不足: ${available_gb}GB 可用 (建议至少 ${min_free_gb}GB)"
    return 1
  fi
  log "磁盘空间充足: ${available_gb}GB 可用"
  return 0
}

# 并行执行多个函数。
run_parallel() {
  local pids=()
  for cmd in "$@"; do
    $cmd &
    pids+=($!)
  done
  local overall_status=0
  for pid in "${pids[@]}"; do
    wait $pid || overall_status=1
  done
  return $overall_status
}

# ========================
# 摘要与格式化函数
# ========================
format_status() {
  case "$1" in
    *"成功"*|*"已执行"*|*"无需清理"*|*"个"*) echo -e "${GREEN}$1${NC}" ;;
    *"失败"*) echo -e "${RED}$1${NC}" ;;
    *) echo -e "${YELLOW}$1${NC}" ;;
  esac
}

calculate_display_width() {
  local text="$1"
  local clean_text=$(echo "$text" | sed -E 's/\x1b\[[0-9;]*m//g' | sed 's/️//g')
  local width=0
  for ((i=0; i<${#clean_text}; i++)); do
    local char="${clean_text:$i:1}"
    local unicode_val=$(printf "%d" "'$char" 2>/dev/null || echo "0")
    if [[ $unicode_val -gt 127 ]]; then
      width=$((width + 2))
    else
      width=$((width + 1))
    fi
  done
  echo $width
}

print_aligned_row() {
  local content="$1"
  local target_width=60
  local actual_width=$(calculate_display_width "$content")
  local padding=$((target_width - actual_width))
  [[ $padding -lt 0 ]] && padding=0
  local spaces=$(printf "%*s" $padding "")
  echo -e "${BORDER}│${NC} ${content}${spaces} ${BORDER}│${NC}"
}

print_summary() {
  local end_time=$(date +%s)
  local duration=$((end_time - SCRIPT_STATS[start_time]))
  local minutes=$((duration / 60))
  local seconds=$((duration % 60))

  echo
  echo -e "${BORDER_BOLD}╭──────────────────────────────────────────────────────────────╮${NC}"
  echo -e "${BORDER_BOLD}│                        📊 执行摘要                           │${NC}"
  echo -e "${BORDER}├──────────────────────────────────────────────────────────────┤${NC}"
  print_aligned_row "⏱️  总耗时: ${GREEN}${minutes}分${seconds}秒${NC}"
  print_aligned_row "⚠️  警告: ${YELLOW}${SCRIPT_STATS[warnings]}${NC} | ❌ 错误: ${RED}${SCRIPT_STATS[errors]}${NC}"
  echo -e "${BORDER}├──────────────────────────────────────────────────────────────┤${NC}"
  echo -e "${BORDER}│${NC} 🧹 清理与维护                                                ${BORDER}│${NC}"
  echo -e "${BORDER}├──────────────────────────────────────────────────────────────┤${NC}"
  print_aligned_row "  孤儿包: $(format_status "${SCRIPT_STATS[orphans_cleaned]}")"
  print_aligned_row "  Pacman缓存: $(format_status "${SCRIPT_STATS[pacman_cache_freed]}")"
  print_aligned_row "  Paru缓存: $(format_status "${SCRIPT_STATS[paru_cache_status]}")"
  print_aligned_row "  UV缓存: $(format_status "${SCRIPT_STATS[uv_cache_status]}")"
  print_aligned_row "  系统清理: $(format_status "${SCRIPT_STATS[system_cleanup]}")"
  print_aligned_row "  Systemd日志: $(format_status "${SCRIPT_STATS[journal_cleanup]}")"
  print_aligned_row "  GNOME缓存: $(format_status "${SCRIPT_STATS[gnome_cache]}")"
  print_aligned_row "  开发工具缓存: $(format_status "${SCRIPT_STATS[dev_cache]}")"
  echo -e "${BORDER}├──────────────────────────────────────────────────────────────┤${NC}"
  echo -e "${BORDER}│${NC} 🔄 组件更新                                                  ${BORDER}│${NC}"
  echo -e "${BORDER}├──────────────────────────────────────────────────────────────┤${NC}"
  print_aligned_row "  镜像源: $(format_status "${SCRIPT_STATS[mirror_status]}")"
  print_aligned_row "  Flatpak: $(format_status "${SCRIPT_STATS[flatpak_status]}")"
  print_aligned_row "  Xmake: $(format_status "${SCRIPT_STATS[xmake_status]}")"
  print_aligned_row "  Zinit: $(format_status "${SCRIPT_STATS[zinit_status]}")"
  print_aligned_row "  Go清理: $(format_status "${SCRIPT_STATS[go_status]}")"
  print_aligned_row "  图标修复: $(format_status "${SCRIPT_STATS[icon_status]}")"
  echo -e "${BORDER_BOLD}╰──────────────────────────────────────────────────────────────╯${NC}"
  echo
}

# ========================
# 组件更新与清理函数
# ========================
update_flatpak() {
  if run_with_retry 3 5 flatpak update -y &>/dev/null; then
    echo "成功" > "$temp_dir/flatpak_status"
    log_success "Flatpak更新完成"
  else
    echo "失败" > "$temp_dir/flatpak_status"
    log_warning "Flatpak更新失败"
  fi
}

update_xmake() {
  if run_with_retry 2 5 xmake repo -u &>/dev/null; then
    echo "成功" > "$temp_dir/xmake_status"
    log_success "Xmake更新完成"
  else
    echo "失败" > "$temp_dir/xmake_status"
    log_warning "Xmake更新失败"
  fi
}

update_zinit() {
  # 在一个新的 zsh 进程中运行 zinit，以确保环境干净。
  if zsh -c "source '$ZINIT_SCRIPT_PATH'; zinit update --parallel 160" &>/dev/null; then
    echo "成功" > "$temp_dir/zinit_status"
    log_success "Zinit更新完成"
  else
    echo "失败" > "$temp_dir/zinit_status"
    log_warning "Zinit更新失败"
  fi
}

# 测试和更新最快的镜像源
update_mirrors() {
  if [[ "${COMMAND_AVAILABLE[reflector]}" == "true" ]]; then
    log "更新镜像源列表..."
    # 使用 reflector 获取最快的镜像源
    if run_sudo reflector --country Singapore,Japan,'United States' --age 12 --protocol https --sort rate --save /etc/pacman.d/mirrorlist &>/dev/null; then
      log_success "镜像源更新完成"
      record_mirrors_update  # 记录本周已更新
      SCRIPT_STATS[mirror_status]="已更新"
    else
      log_warning "镜像源更新失败"
      SCRIPT_STATS[mirror_status]="失败"
    fi
  else
    log "未安装 reflector，跳过镜像源优化"
    SCRIPT_STATS[mirror_status]="跳过"
  fi
}

# 清理 systemd 日志
cleanup_systemd_logs() {
  if [[ "${COMMAND_AVAILABLE[journalctl]}" == "true" ]]; then
    log "清理 systemd 日志..."
    local log_size_before=$(journalctl --disk-usage 2>/dev/null | grep -oE '[0-9.]+[KMGT]B' || echo "0B")

    # 保留最近 7 天的日志
    if run_sudo journalctl --vacuum-time=7d &>/dev/null; then
      local log_size_after=$(journalctl --disk-usage 2>/dev/null | grep -oE '[0-9.]+[KMGT]B' || echo "0B")
      log_success "Systemd 日志清理完成: $log_size_before → $log_size_after"
      SCRIPT_STATS[journal_cleanup]="$log_size_before → $log_size_after"
    else
      log_warning "Systemd 日志清理失败"
      SCRIPT_STATS[journal_cleanup]="失败"
    fi
  else
    log "未找到 journalctl 命令，跳过日志清理"
    SCRIPT_STATS[journal_cleanup]="跳过"
  fi
}

# 清理 GNOME 桌面环境缓存
cleanup_gnome_cache() {
  log "清理 GNOME 桌面环境缓存..."
  local cleaned_size=0

  # GNOME Software 缓存
  if [[ -d "$HOME/.cache/gnome-software" ]]; then
    local gnome_size=$(du -sb "$HOME/.cache/gnome-software" 2>/dev/null | cut -f1 || echo 0)
    find "$HOME/.cache/gnome-software" -mindepth 1 -delete 2>/dev/null || true
    cleaned_size=$((cleaned_size + gnome_size))
    log "清理 GNOME Software 缓存"
  fi

  # GNOME Shell 缓存
  if [[ -d "$HOME/.cache/gnome-shell" ]]; then
    local shell_size=$(du -sb "$HOME/.cache/gnome-shell" 2>/dev/null | cut -f1 || echo 0)
    find "$HOME/.cache/gnome-shell" -mindepth 1 -delete 2>/dev/null || true
    cleaned_size=$((cleaned_size + shell_size))
    log "清理 GNOME Shell 缓存"
  fi

  # GStreamer 缓存
  if [[ -d "$HOME/.cache/gstreamer-1.0" ]]; then
    local gst_size=$(du -sb "$HOME/.cache/gstreamer-1.0" 2>/dev/null | cut -f1 || echo 0)
    find "$HOME/.cache/gstreamer-1.0" -mindepth 1 -delete 2>/dev/null || true
    cleaned_size=$((cleaned_size + gst_size))
    log "清理 GStreamer 缓存"
  fi

  # Tracker 缓存
  if [[ -d "$HOME/.cache/tracker" ]]; then
    local tracker_size=$(du -sb "$HOME/.cache/tracker" 2>/dev/null | cut -f1 || echo 0)
    find "$HOME/.cache/tracker" -mindepth 1 -delete 2>/dev/null || true
    cleaned_size=$((cleaned_size + tracker_size))
    log "清理 Tracker 缓存"
  fi

  # Evolution 缓存
  if [[ -d "$HOME/.cache/evolution" ]]; then
    local evo_size=$(du -sb "$HOME/.cache/evolution" 2>/dev/null | cut -f1 || echo 0)
    find "$HOME/.cache/evolution" -mindepth 1 -delete 2>/dev/null || true
    cleaned_size=$((cleaned_size + evo_size))
    log "清理 Evolution 缓存"
  fi

  local cleaned_mb=$((cleaned_size / 1024 / 1024))
  SCRIPT_STATS[gnome_cache]="${cleaned_mb}MB"
  log_success "GNOME 桌面环境缓存清理完成: ${cleaned_mb}MB"
}

# 清理开发工具缓存
cleanup_dev_caches() {
  log "清理开发工具缓存..."
  local cleaned_items=()

  # Node.js npm 缓存
  if command -v npm &>/dev/null; then
    if npm cache clean --force &>/dev/null; then
      cleaned_items+=("npm")
      log "清理 npm 缓存"
    fi
  fi

  # Yarn 缓存
  if command -v yarn &>/dev/null; then
    if yarn cache clean &>/dev/null; then
      cleaned_items+=("yarn")
      log "清理 yarn 缓存"
    fi
  fi

  # pnpm 缓存
  if command -v pnpm &>/dev/null; then
    if pnpm store prune &>/dev/null; then
      cleaned_items+=("pnpm")
      log "清理 pnpm 缓存"
    fi
  fi

  # Pip 缓存
  if command -v pip &>/dev/null; then
    if pip cache purge &>/dev/null; then
      cleaned_items+=("pip")
      log "清理 pip 缓存"
    fi
  fi

  # Cargo 缓存
  if command -v cargo &>/dev/null; then
    if cargo clean &>/dev/null; then
      cleaned_items+=("cargo")
      log "清理 cargo 缓存"
    fi
  fi



  # Maven 缓存
  if [[ -d "$HOME/.m2/repository" ]]; then
    local maven_size=$(du -sb "$HOME/.m2/repository" 2>/dev/null | cut -f1 || echo 0)
    if (( maven_size > 1073741824 )); then  # 大于 1GB 才清理
      find "$HOME/.m2/repository" -name "*.lastUpdated" -delete 2>/dev/null || true
      cleaned_items+=("maven")
      log "清理 Maven 缓存"
    fi
  fi

  # Gradle 缓存
  if [[ -d "$HOME/.gradle/caches" ]]; then
    local gradle_size=$(du -sb "$HOME/.gradle/caches" 2>/dev/null | cut -f1 || echo 0)
    if (( gradle_size > 1073741824 )); then  # 大于 1GB 才清理
      rm -rf "$HOME/.gradle/caches"/* 2>/dev/null || true
      cleaned_items+=("gradle")
      log "清理 Gradle 缓存"
    fi
  fi

  if (( ${#cleaned_items[@]} > 0 )); then
    SCRIPT_STATS[dev_cache]="已清理: ${cleaned_items[*]}"
    log_success "开发工具缓存清理完成: ${cleaned_items[*]}"
  else
    SCRIPT_STATS[dev_cache]="无需清理"
    log "未发现需要清理的开发工具缓存"
  fi
}

cleanup_go() {
  go clean -cache -modcache -testcache -fuzzcache &>/dev/null
  log "Go 标准缓存清理完成"
  local -a dirs_to_prune=("$HOME/go/pkg" "$HOME/go")
  local pruned_count=0
  for dir in "${dirs_to_prune[@]}"; do
    if [[ -d "$dir" ]] && [[ -z "$(ls -A "$dir" 2>/dev/null)" ]]; then
      if rmdir "$dir" 2>/dev/null; then
        log "已移除空的 Go 目录: $dir"
        (( pruned_count++ ))
      else
        log_warning "尝试移除空目录 $dir 失败"
      fi
    fi
  done
  if (( pruned_count > 0 )); then
    SCRIPT_STATS[go_status]="已执行 (清理了${pruned_count}个空目录)"
  else
    SCRIPT_STATS[go_status]="已执行"
  fi
  log_success "Go 清理流程完成"
}


# ========================
# 模块化的步骤函数
# ========================
run_step_pre_checks() {
  show_progress "系统与环境检查"
  if (( EUID == 0 )); then
    log_error "请不要以 root 用户身份直接运行此脚本"; return 1;
  fi; log_success "用户权限检查通过"

  check_commands || return 1
  log_success "系统组件检查完成"

  if [[ "${COMMAND_AVAILABLE[fastfetch]}" == "true" ]]; then
    fastfetch
  else
    log_warning "未找到 fastfetch，跳过系统信息显示"
  fi

  if ! run_with_retry 2 2 check_network; then
    log_error "网络连接异常"; return 1;
  fi; log_success "网络连接正常"

  check_disk_space

  if ! sudo -v &>/dev/null; then
    log_error "无法获取sudo权限，请先运行 'sudo -v' 或检查 sudoers 配置"; return 1;
  fi; log_success "sudo权限验证通过"

  return 0
}

run_step_system_update() {
  show_progress "系统更新"
  if pgrep -x "pacman|paru" >/dev/null; then
    log_error "检测到正在运行的 pacman/paru 进程，为避免冲突已终止脚本。"; return 1;
  elif [[ -f $PACMAN_LOCK ]]; then
    log_warning "发现残留的 pacman 锁文件，可能上次更新异常中断，正在尝试清理..."
    if ! run_sudo rm -v "$PACMAN_LOCK"; then
      log_error "锁文件清理失败，请手动删除 $PACMAN_LOCK"; return 1;
    fi
    log_success "锁文件清理完成"
  fi

  # 更新镜像源（每周执行一次）
  if [[ "$(get_chinese_weekday)" == "$MIRRORS_UPDATE_DAY" ]]; then
    if check_mirrors_updated_this_week; then
      log "本周已更新过镜像源，跳过重复更新"
      SCRIPT_STATS[mirror_status]="本周已更新"
    else
      log "今天是 $MIRRORS_UPDATE_DAY，执行镜像源更新"
      update_mirrors
    fi
  else
    log "跳过镜像源更新（非预定日）"
    SCRIPT_STATS[mirror_status]="未执行"
  fi

  log "开始系统更新 (官方仓库 + AUR)..."
  # paru 最佳实践：
  # --noconfirm: 自动确认安装
  # --needed: 只安装需要更新的包
  # --skipreview: 跳过 PKGBUILD 审查（在自动化脚本中使用）
  # --removemake: 移除构建依赖
  # --cleanafter: 构建后清理
  if ! run_with_retry 3 10 paru -Syu --noconfirm --needed --skipreview --removemake --cleanafter; then
    log_error "系统更新失败"; return 1;
  fi
  log_success "系统更新完成"
  return 0
}

run_step_cleanup() {
  show_progress "清理与维护"
  local orphans
  if [[ "$(get_chinese_weekday)" == "$ORPHAN_CLEANUP_DAY" ]]; then
    log "今天是 $ORPHAN_CLEANUP_DAY，执行孤儿包清理"
    orphans=($(pacman -Qtdq 2>/dev/null || true))
    if (( #orphans > 0 )); then
      log_warning "发现 $#orphans 个孤儿包，开始清理..."
      if run_sudo pacman -Rns --noconfirm "${orphans[@]}" &>/dev/null; then
        SCRIPT_STATS[orphans_cleaned]="$#orphans 个"; log_success "孤儿包清理完成"
      else
        SCRIPT_STATS[orphans_cleaned]="清理失败"; log_error "孤儿包清理失败"
      fi
    else
      SCRIPT_STATS[orphans_cleaned]="0 个"; log_success "未发现孤儿包"
    fi
  else
    log "跳过孤儿包清理（非预定日）"; SCRIPT_STATS[orphans_cleaned]="未执行"
  fi

  # UV 缓存清理
  if [[ "${COMMAND_AVAILABLE[uv]}" == "true" ]]; then
    if [[ "$(get_chinese_weekday)" == "$UV_CLEANUP_DAY" ]]; then
      log "今天是 $UV_CLEANUP_DAY，执行 UV 缓存清理"
      if uv cache clean &>/dev/null; then
        SCRIPT_STATS[uv_cache_status]="清理成功"
        log_success "UV 缓存清理完成"
      else
        SCRIPT_STATS[uv_cache_status]="清理失败"
        log_error "UV 缓存清理失败"
      fi
    else
      log "跳过 UV 缓存清理（非预定日）"
      SCRIPT_STATS[uv_cache_status]="未执行"
    fi
  else
    log "未找到 uv 命令，跳过 UV 缓存清理"
    SCRIPT_STATS[uv_cache_status]="跳过"
  fi

  log "清理pacman包缓存（保留最近${PACCACHE_KEEP}个版本）..."
  local pacman_cache_output
  pacman_cache_output=$( { run_sudo paccache -rk${PACCACHE_KEEP} && run_sudo paccache -ruk0; } 2>&1 )
  local freed_space="0MB"
  if [[ "${COMMAND_AVAILABLE[bc]}" == "true" && -n "$pacman_cache_output" ]]; then
    local total_freed_mb=$(echo "scale=2; $(
      echo "$pacman_cache_output" | grep -oE '[0-9]+\.?[0-9]*\s+(GiB|MiB|KiB)' | while IFS=' ' read -r size unit; do
        case "$unit" in
          "GiB") echo -n "$size * 1024 + " ;;
          "MiB") echo -n "$size + " ;;
          "KiB") echo -n "$size / 1024 + " ;;
        esac
      done
      echo "0"
    )" | bc)
    freed_space="$(printf "%.0fMB" "$total_freed_mb")"
  elif [[ -n "$pacman_cache_output" ]]; then
    freed_space="已清理"
  fi
  SCRIPT_STATS[pacman_cache_freed]="$freed_space"
  log_success "Pacman包缓存清理完成"

  log "清理Paru构建缓存..."
  if [[ -d "$PARU_CACHE_DIR" ]]; then
    if find "$PARU_CACHE_DIR" -mindepth 1 -delete 2>/dev/null; then
      SCRIPT_STATS[paru_cache_status]="清理成功"; log_success "Paru构建缓存清理完成"
    else
      SCRIPT_STATS[paru_cache_status]="清理失败"; log_warning "Paru构建缓存清理失败"
    fi
  else
    SCRIPT_STATS[paru_cache_status]="无需清理"; log "未找到Paru构建缓存目录"
  fi

  # 清理 systemd 日志
  cleanup_systemd_logs

  if [[ "$ENABLE_SYSTEM_CLEANUP" == "true" ]]; then
    log "清理系统临时文件..."
    local cleaned_size=0
    if [[ -d "$HOME/.cache/thumbnails" ]]; then
      local thumb_size=$(du -sb "$HOME/.cache/thumbnails" 2>/dev/null | cut -f1 || echo 0)
      rm -rf "$HOME/.cache/thumbnails"/*(N)
      cleaned_size=$((cleaned_size + thumb_size))
    fi
    run_sudo find /var/log -name "*.log.*" -mtime +30 -delete 2>/dev/null || true
    run_sudo find /tmp -type f -atime +7 -delete 2>/dev/null || true
    find "$HOME/.local/share/xorg" -name "*.log.*" -mtime +7 -delete 2>/dev/null || true
    local cleaned_mb=$((cleaned_size / 1024 / 1024))
    SCRIPT_STATS[system_cleanup]="${cleaned_mb}MB"
    log_success "系统清理完成: ${cleaned_mb}MB"

    # 清理 GNOME 桌面环境缓存（每周执行一次）
    if [[ "$(get_chinese_weekday)" == "$GNOME_CACHE_CLEANUP_DAY" ]]; then
      log "今天是 $GNOME_CACHE_CLEANUP_DAY，执行 GNOME 缓存清理"
      cleanup_gnome_cache
    else
      log "跳过 GNOME 缓存清理（非预定日）"
      SCRIPT_STATS[gnome_cache]="未执行"
    fi

    # 清理开发工具缓存
    cleanup_dev_caches
  fi
  return 0
}

run_step_component_updates() {
  show_progress "组件更新"
  local update_tasks=()
  [[ "${COMMAND_AVAILABLE[flatpak]}" == "true" ]] && update_tasks+=("update_flatpak")
  [[ "${COMMAND_AVAILABLE[xmake]}" == "true" ]] && update_tasks+=("update_xmake")
  [[ "${COMMAND_AVAILABLE[zinit]}" == "true" ]] && update_tasks+=("update_zinit")

  if (( ${#update_tasks[@]} > 0 )); then
    if [[ "$PARALLEL_MODE" == "true" ]]; then
      log "并行更新组件..."
      run_parallel "${update_tasks[@]}"
    else
      log "串行更新组件..."
      for task in "${update_tasks[@]}"; do $task; done
    fi
    for task in "${update_tasks[@]}"; do
      local component_name=${task#update_}
      local status_file="$temp_dir/${component_name}_status"
      if [[ -f "$status_file" ]]; then
        SCRIPT_STATS[${component_name}_status]=$(<"$status_file")
      fi
    done
  else
    log "没有可更新的额外组件。"
  fi
  [[ "${COMMAND_AVAILABLE[go]}" == "true" ]] && { log "清理Go缓存..."; cleanup_go; }
  return 0
}

run_step_final_tasks() {
  show_progress "收尾任务"
  # 检查图标修复脚本是否存在且可执行
  if [[ -x "$ICON_SCRIPT" ]]; then
    log "执行图标修复脚本..."
    local icon_output

    # ▼▼▼ 核心修复 ▼▼▼
    # 因为图标脚本需要修改 /usr/share/applications/ 下的文件，
    # 必须以 root 权限运行。我们使用 run_sudo 函数来安全地获取权限。
    if icon_output=$(run_sudo "$ICON_SCRIPT" 2>&1); then
    # ▲▲▲ 核心修复 ▲▲▲
      SCRIPT_STATS[icon_status]="成功"
      log_success "图标修复完成"
      if [[ "$VERBOSE_MODE" == "true" && -n "$icon_output" ]]; then
        # 即使成功，也可能有一些提示性输出，在详细模式下打印出来。
        log "图标脚本输出:\n$icon_output"
      fi
    else
      SCRIPT_STATS[icon_status]="失败"
      # 注意：由于使用了 run_sudo，如果失败，可能是 sudo 密码错误或脚本本身错误。
      # 捕获的输出将包含 sudo 的提示和脚本的错误信息。
      log_error "图标修复脚本执行失败"
      log_error "错误详情:\n$icon_output"
    fi
  else
    SCRIPT_STATS[icon_status]="跳过"
    if [[ -f "$ICON_SCRIPT" ]]; then
        log_warning "图标修复脚本 '$ICON_SCRIPT' 存在但不可执行。请运行: chmod +x '$ICON_SCRIPT'"
    else
        log "未找到图标修复脚本 '$ICON_SCRIPT'，跳过执行。"
    fi
  fi
  return 0
}

# ========================
# 主流程定义
# ========================
declare -a main_steps
main_steps=(
  "run_step_pre_checks"
  "run_step_system_update"
  "run_step_cleanup"
  "run_step_component_updates"
  "run_step_final_tasks"
)
typeset -gr TOTAL_STEPS=${#main_steps[@]}

main() {
  temp_dir=$(mktemp -d)
  trap "rm -rf '$temp_dir'" EXIT INT TERM

  SCRIPT_STATS=(
    [start_time]=$(date +%s)
    [warnings]=0 [errors]=0
    [orphans_cleaned]="未执行" [pacman_cache_freed]="0MB"
    [paru_cache_status]="未执行" [uv_cache_status]="未执行" [system_cleanup]="0MB"
    [flatpak_status]="跳过" [xmake_status]="跳过"
    [zinit_status]="跳过" [go_status]="跳过" [icon_status]="跳过"
    [mirror_status]="跳过" [journal_cleanup]="跳过" [gnome_cache]="0MB" [dev_cache]="跳过"
  )
  echo -e "${BORDER_BOLD}╭──────────────────────────────────────────────────────────────╮${NC}"
  echo -e "${BORDER_BOLD}│                    🚀 Arch Linux 每日更新                    │${NC}"
  echo -e "${BORDER_BOLD}╰──────────────────────────────────────────────────────────────╯${NC}"
  echo

  for step_func in "${main_steps[@]}"; do
    if ! $step_func; then
      log_error "关键步骤 '$step_func' 失败，脚本终止。"
      print_summary
      exit 1
    fi
  done

  log_success "所有任务已完成！"
  print_summary
}

main "$@"
